"use client";

import React, { useState, useEffect } from "react";

import { PhysicalCard, User } from "@prisma/client";
import Table from "~/utils/table/table";
import { ValueGetterParams, ICellRendererParams } from "ag-grid-community/dist/lib/entities/colDef";

interface UserGroup {
  id: string;
  name: string;
  description?: string;
}

export const PhysicalCardTable = ({ physicalCards }: { physicalCards: PhysicalCard[] }) => {
  const [userGroups, setUserGroups] = useState<UserGroup[]>([]);
  const [loadingUserGroups, setLoadingUserGroups] = useState(true);

  useEffect(() => {
    const fetchUserGroups = async () => {
      try {
        const response = await fetch("/api/userGroup");
        if (response.ok) {
          const data = await response.json();
          setUserGroups(data);
        }
      } catch (error) {
        console.error("Error fetching user groups:", error);
      } finally {
        setLoadingUserGroups(false);
      }
    };

    fetchUserGroups();
  }, []);

  const UserGroupCellRenderer = (params: ICellRendererParams) => {
    const [selectedUserGroupId, setSelectedUserGroupId] = useState(
      params.data?.userGroupId || ""
    );
    const [updating, setUpdating] = useState(false);

    const handleUserGroupChange = async (newUserGroupId: string) => {
      setUpdating(true);
      try {
        const response = await fetch(`/api/physicalCard/${params.data.uid}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            userGroupId: newUserGroupId || null,
          }),
        });

        if (response.ok) {
          setSelectedUserGroupId(newUserGroupId);
          // Update the data in the grid
          params.data.userGroupId = newUserGroupId || null;
          params.api.refreshCells({ rowNodes: [params.node] });
        } else {
          console.error("Failed to update user group");
        }
      } catch (error) {
        console.error("Error updating user group:", error);
      } finally {
        setUpdating(false);
      }
    };

    return (
      <select
        value={selectedUserGroupId}
        onChange={(e) => handleUserGroupChange(e.target.value)}
        disabled={updating || loadingUserGroups}
        className="w-full text-sm border border-gray-300 rounded px-2 py-1"
      >
        <option value="">Keine Nutzergruppe</option>
        {userGroups.map((group) => (
          <option key={group.id} value={group.id}>
            {group.name}
          </option>
        ))}
      </select>
    );
  };

  const gridOptions = { suppressAggFuncInHeader: true };
  const columnDefs = [
    {
      field: "uid",
      headerName: "UID",
    },
    {
      field: "visualNumber",
      headerName: "Kartennummer",
      editable: false,
      enableRowGroup: true,
    },

    {
      field: "empcard.user.email",
      headerName: "Verwendet von",
      editable: false,
      enableRowGroup: true,
      valueGetter: (params: ValueGetterParams) => {
        {
          if (params?.data?.EMPCard?.user) {
            return params.data.EMPCard.user.email;
          } else {
            return "<nicht verwendet zugewiesen bisher>";
          }
        }
      },
    },
    {
      field: "ou.name",
      headerName: "CPO",
      editable: false,
      enableRowGroup: true,
      valueGetter: (params: ValueGetterParams) => {
        {
          if (params?.data?.cpo?.name) {
            return params?.data?.cpo?.name;
          } else {
            return "Keinem CPO zugeordnet";
          }
        }
      },
    },
    {
      field: "userGroup",
      headerName: "Nutzergruppe",
      editable: false,
      enableRowGroup: true,
      cellRenderer: UserGroupCellRenderer,
      width: 200,
    },
  ];
  return <Table gridOptions={gridOptions} columnDefs={columnDefs} rowData={physicalCards} />;
};
