import Card from "../../../../component/card";
import prisma from "../../../../server/db/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { redirect, notFound } from "next/navigation";
import Link from "next/link";
import { BsPencilSquare } from "react-icons/bs";
import UserGroupTarifs from "../components/UserGroupTarifs";

interface Props {
  params: {
    id: string;
  };
}

const getUserGroup = async (id: string) => {
  const session = await getServerSession(authOptions);
  
  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    return null;
  }

  const userGroup = await prisma.userGroup.findUnique({
    where: { id },
    include: {
      ou: {
        select: {
          id: true,
          name: true,
        },
      },
      users: {
        select: {
          id: true,
          name: true,
          lastName: true,
          email: true,
          role: true,
          createdAt: true,
        },
        orderBy: {
          email: "asc",
        },
      },
      physicalCards: {
        select: {
          uid: true,
          visualNumber: true,
          valid: true,
          EMPCard: {
            select: {
              id: true,
              active: true,
              user: {
                select: {
                  email: true,
                },
              },
            },
          },
        },
        orderBy: {
          visualNumber: "asc",
        },
      },
      companyTarifs: {
        include: {
          tarif: {
            select: {
              id: true,
              name: true,
              energyPrice: true,
              sessionPrice: true,
              validFrom: true,
              validTo: true,
              description: true,
              optional: true,
              internal: true,
            },
          },
        },
      },
    },
  });

  // Check permissions
  if (userGroup && session.user.role !== Role.ADMIN && userGroup.ouId !== session.user.selectedOu.id) {
    return null;
  }

  return userGroup;
};

const UserGroupDetailPage = async ({ params }: Props) => {
  const session = await getServerSession(authOptions);
  
  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    redirect("/");
  }

  const userGroup = await getUserGroup(params.id);

  if (!userGroup) {
    notFound();
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card header_left={`Nutzergruppe: ${userGroup.name}`}>
        <div className="flex justify-between items-start">
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Grundinformationen
              </h3>
              <div className="mt-2 grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Name</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">{userGroup.name}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">OU</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">{userGroup.ou.name}</dd>
                </div>
                {userGroup.description && (
                  <div className="sm:col-span-2">
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Beschreibung</dt>
                    <dd className="mt-1 text-sm text-gray-900 dark:text-white">{userGroup.description}</dd>
                  </div>
                )}
                <div>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Erstellt am</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                    {new Date(userGroup.createdAt).toLocaleDateString("de-DE")}
                  </dd>
                </div>
              </div>
            </div>
          </div>
          <Link href={`/userGroups/${userGroup.id}/edit`}>
            <button className="btn btn-secondary flex items-center">
              <BsPencilSquare className="mr-2" />
              Bearbeiten
            </button>
          </Link>
        </div>
      </Card>

      {/* Users */}
      <Card header_left={`Benutzer (${userGroup.users.length})`}>
        {userGroup.users.length === 0 ? (
          <p className="text-gray-500 dark:text-gray-400">Keine Benutzer in dieser Nutzergruppe.</p>
        ) : (
          <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    E-Mail
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Rolle
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Erstellt
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                {userGroup.users.map((user) => (
                  <tr key={user.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {user.name} {user.lastName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {user.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {user.role}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {new Date(user.createdAt).toLocaleDateString("de-DE")}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </Card>

      {/* Physical Cards */}
      <Card header_left={`Physische Karten (${userGroup.physicalCards.length})`}>
        {userGroup.physicalCards.length === 0 ? (
          <p className="text-gray-500 dark:text-gray-400">Keine physischen Karten in dieser Nutzergruppe.</p>
        ) : (
          <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Kartennummer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    UID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Zugeordnet an
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                {userGroup.physicalCards.map((card) => (
                  <tr key={card.uid}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {card.visualNumber}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {card.uid}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        card.valid 
                          ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' 
                          : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                      }`}>
                        {card.valid ? 'Gültig' : 'Ungültig'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {card.EMPCard?.user?.email || '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </Card>

      {/* Company Tarifs */}
      <UserGroupTarifs userGroupId={userGroup.id} />
    </div>
  );
};

export default UserGroupDetailPage;
