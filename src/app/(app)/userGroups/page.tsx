import Link from "next/link";
import { IoIosAdd } from "react-icons/io";
import Card from "../../../component/card";
import UserGroupTable from "./components/UserGroupTable";
import prisma from "../../../server/db/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { redirect } from "next/navigation";

export const revalidate = 0;

const getUserGroups = async () => {
  const session = await getServerSession(authOptions);
  
  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    return [];
  }

  const whereClause = session.user.role === Role.ADMIN 
    ? {} 
    : { ouId: session.user.selectedOu.id };

  return await prisma.userGroup.findMany({
    where: whereClause,
    include: {
      ou: {
        select: {
          id: true,
          name: true,
        },
      },
      _count: {
        select: {
          users: true,
          physicalCards: true,
          companyTarifs: true,
        },
      },
    },
    orderBy: {
      name: "asc",
    },
  });
};

const UserGroupsPage = async () => {
  const session = await getServerSession(authOptions);
  
  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    redirect("/");
  }

  const userGroups = await getUserGroups();

  return (
    <Card header_left={"Nutzergruppen"}>
      <div className={"mb-10 flex flex-row-reverse"}>
        <Link href={"/userGroups/create"}>
          <button className={"btn text-right"}>
            <IoIosAdd size={20} />
            Neue Nutzergruppe
          </button>
        </Link>
      </div>
      <UserGroupTable data={userGroups} />
    </Card>
  );
};

export default UserGroupsPage;
