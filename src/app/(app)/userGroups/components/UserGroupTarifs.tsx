"use client";

import { useState, useEffect } from "react";
import Card from "../../../../component/card";
import Button from "~/component/button";
import { FiLoader } from "react-icons/fi";

interface CompanyTarif {
  id: string;
  name: string;
  energyPrice: number;
  sessionPrice: number;
  validFrom: string;
  validTo: string;
  description?: string;
  optional: boolean;
  internal: boolean;
}

interface UserGroupTarifsData {
  assigned: CompanyTarif[];
  available: CompanyTarif[];
}

interface Props {
  userGroupId: string;
  userGroupName?: string;
}

const UserGroupTarifs = ({ userGroupId, userGroupName }: Props) => {
  const [data, setData] = useState<UserGroupTarifsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string>("");
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [selectedTarifs, setSelectedTarifs] = useState<string[]>([]);

  useEffect(() => {
    fetchTarifs();
  }, [userGroupId]);

  const fetchTarifs = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/userGroup/${userGroupId}/tarifs`);

      if (!response.ok) {
        throw new Error("Fehler beim Laden der Tarife");
      }

      const tarifsData = await response.json();
      setData(tarifsData);
      setSelectedTarifs(tarifsData.assigned.map((t: CompanyTarif) => t.id));
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unbekannter Fehler");
    } finally {
      setLoading(false);
    }
  };

  const handleTarifToggle = (tarifId: string) => {
    setSelectedTarifs(prev =>
      prev.includes(tarifId)
        ? prev.filter(id => id !== tarifId)
        : [...prev, tarifId]
    );
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError("");
      setSuccessMessage("");

      const response = await fetch(`/api/userGroup/${userGroupId}/tarifs`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ tarifIds: selectedTarifs }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Fehler beim Speichern");
      }

      const result = await response.json();
      setSuccessMessage(result.message || "Tarif-Zuordnungen erfolgreich aktualisiert");

      // Refresh data
      await fetchTarifs();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unbekannter Fehler");
    } finally {
      setSaving(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("de-DE", {
      style: "currency",
      currency: "EUR",
      minimumFractionDigits: 4,
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("de-DE");
  };

  if (loading) {
    return (
      <Card header_left="Tarif-Zuordnungen">
        <div className="flex justify-center py-8">
          <FiLoader className="animate-spin text-2xl" />
        </div>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card header_left="Tarif-Zuordnungen">
        <p className="text-red-600">Fehler beim Laden der Tarife</p>
      </Card>
    );
  }

  const hasChanges = JSON.stringify(selectedTarifs.sort()) !== JSON.stringify(data.assigned.map(t => t.id).sort());

  return (
    <Card header_left={`Tarif-Zuordnungen${userGroupName ? ` - ${userGroupName}` : ''}`}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Wählen Sie die Tarife aus der gleichen OU aus, die für diese Nutzergruppe verfügbar sein sollen.
          </p>
          <Button
            onClick={handleSave}
            disabled={!hasChanges || saving}
            className="flex items-center"
          >
            {saving && <FiLoader className="mr-2 animate-spin" />}
            Speichern
          </Button>
        </div>

        {successMessage && (
          <div className="rounded-md bg-green-50 p-4">
            <div className="text-sm text-green-700">{successMessage}</div>
          </div>
        )}

        {error && (
          <div className="rounded-md bg-red-50 p-4">
            <div className="text-sm text-red-700">{error}</div>
          </div>
        )}

        <div className="space-y-4">
          {data.available.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400">
              Keine Tarife für diese OU verfügbar.
            </p>
          ) : (
            data.available.map((tarif) => (
              <div
                key={tarif.id}
                className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                  selectedTarifs.includes(tarif.id)
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
                }`}
                onClick={() => handleTarifToggle(tarif.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <input
                      type="checkbox"
                      checked={selectedTarifs.includes(tarif.id)}
                      onChange={() => handleTarifToggle(tarif.id)}
                      className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {tarif.name}
                        </h4>
                        {tarif.optional && (
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                            Optional
                          </span>
                        )}
                        {tarif.internal && (
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100">
                            Intern
                          </span>
                        )}
                      </div>
                      {tarif.description && (
                        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                          {tarif.description}
                        </p>
                      )}
                      <div className="mt-2 grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500 dark:text-gray-400">Energiepreis:</span>
                          <span className="ml-1 text-gray-900 dark:text-white">
                            {formatPrice(tarif.energyPrice)}/kWh
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500 dark:text-gray-400">Sitzungsgebühr:</span>
                          <span className="ml-1 text-gray-900 dark:text-white">
                            {formatPrice(tarif.sessionPrice)}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500 dark:text-gray-400">Gültig von:</span>
                          <span className="ml-1 text-gray-900 dark:text-white">
                            {formatDate(tarif.validFrom)}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500 dark:text-gray-400">Gültig bis:</span>
                          <span className="ml-1 text-gray-900 dark:text-white">
                            {formatDate(tarif.validTo)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </Card>
  );
};

export default UserGroupTarifs;
