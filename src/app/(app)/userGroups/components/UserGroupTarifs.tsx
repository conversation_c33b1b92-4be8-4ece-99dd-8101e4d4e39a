"use client";

import { useState, useEffect } from "react";
import Card from "../../../../component/card";
import Button from "~/component/button";
import { FiLoader } from "react-icons/fi";

interface CompanyTarif {
  id: string;
  name: string;
  energyPrice: number;
  sessionPrice: number;
  validFrom: string;
  validTo: string;
  description?: string;
  optional: boolean;
  internal: boolean;
}

interface UserGroupTarifsData {
  assigned: CompanyTarif[];
  available: CompanyTarif[];
}

interface Props {
  userGroupId: string;
  userGroupName?: string;
}

const UserGroupTarifs = ({ userGroupId, userGroupName }: Props) => {
  const [data, setData] = useState<UserGroupTarifsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string>("");
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [selectedTarifs, setSelectedTarifs] = useState<string[]>([]);

  useEffect(() => {
    fetchTarifs();
  }, [userGroupId]);

  const fetchTarifs = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/userGroup/${userGroupId}/tarifs`);

      if (!response.ok) {
        throw new Error("Fehler beim Laden der Tarife");
      }

      const tarifsData = await response.json();
      setData(tarifsData);
      setSelectedTarifs(tarifsData.assigned.map((t: CompanyTarif) => t.id));
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unbekannter Fehler");
    } finally {
      setLoading(false);
    }
  };

  const handleTarifToggle = (tarifId: string) => {
    if (!data) return;

    const tarif = data.available.find(t => t.id === tarifId);
    if (!tarif) return;

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const isValidTarif = new Date(tarif.validFrom) <= today && new Date(tarif.validTo) >= today;

    let newSelection: string[];

    if (selectedTarifs.includes(tarifId)) {
      // Deselecting - always allowed
      newSelection = selectedTarifs.filter(id => id !== tarifId);
    } else {
      // Selecting - check AC/DC constraints for valid tarifs only
      if (isValidTarif && (tarif.currentType === 'AC' || tarif.currentType === 'DC')) {
        // For AC/DC tarifs, remove any other tarif of the same type first
        const otherTarifsOfSameType = selectedTarifs.filter(id => {
          const otherTarif = data.available.find(t => t.id === id);
          if (!otherTarif) return false;

          const isOtherValid = new Date(otherTarif.validFrom) <= today && new Date(otherTarif.validTo) >= today;
          return isOtherValid && otherTarif.currentType === tarif.currentType;
        });

        // Remove other tarifs of same type and add the new one
        newSelection = selectedTarifs
          .filter(id => !otherTarifsOfSameType.includes(id))
          .concat(tarifId);

        if (otherTarifsOfSameType.length > 0) {
          const tarifNames = otherTarifsOfSameType.map(id => {
            const t = data.available.find(t => t.id === id);
            return t?.name || 'Unbekannt';
          }).join(', ');

          setSuccessMessage(`${tarif.currentType}-Tarif "${tarif.name}" ausgewählt. Vorherige Auswahl "${tarifNames}" wurde automatisch entfernt.`);
          setTimeout(() => setSuccessMessage(""), 3000);
        }
      } else {
        // For non-AC/DC or invalid tarifs, just add to selection
        newSelection = [...selectedTarifs, tarifId];
      }
    }

    setError(""); // Clear any previous errors
    setSelectedTarifs(newSelection);
  };

  const validateTarifSelection = (tarifIds: string[]) => {
    if (!data) return { valid: true, error: "" };

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const selectedTarifObjects = data.available.filter(t => tarifIds.includes(t.id));
    const validSelectedTarifs = selectedTarifObjects.filter(tarif =>
      new Date(tarif.validFrom) <= today && new Date(tarif.validTo) >= today
    );

    const acTarifs = validSelectedTarifs.filter(tarif => tarif.currentType === 'AC');
    const dcTarifs = validSelectedTarifs.filter(tarif => tarif.currentType === 'DC');

    if (acTarifs.length > 1) {
      return { valid: false, error: "Eine Nutzergruppe kann nur einen gültigen AC-Tarif haben" };
    }

    if (dcTarifs.length > 1) {
      return { valid: false, error: "Eine Nutzergruppe kann nur einen gültigen DC-Tarif haben" };
    }

    return { valid: true, error: "" };
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError("");
      setSuccessMessage("");

      // Validate before saving
      const validation = validateTarifSelection(selectedTarifs);
      if (!validation.valid) {
        setError(validation.error);
        return;
      }

      const response = await fetch(`/api/userGroup/${userGroupId}/tarifs`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ tarifIds: selectedTarifs }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Fehler beim Speichern");
      }

      const result = await response.json();
      setSuccessMessage(result.message || "Tarif-Zuordnungen erfolgreich aktualisiert");

      // Refresh data
      await fetchTarifs();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unbekannter Fehler");
    } finally {
      setSaving(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("de-DE", {
      style: "currency",
      currency: "EUR",
      minimumFractionDigits: 4,
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("de-DE");
  };

  if (loading) {
    return (
      <Card header_left="Tarif-Zuordnungen">
        <div className="flex justify-center py-8">
          <FiLoader className="animate-spin text-2xl" />
        </div>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card header_left="Tarif-Zuordnungen">
        <p className="text-red-600">Fehler beim Laden der Tarife</p>
      </Card>
    );
  }

  const hasChanges = JSON.stringify(selectedTarifs.sort()) !== JSON.stringify(data.assigned.map(t => t.id).sort());

  return (
    <Card header_left={`Tarif-Zuordnungen${userGroupName ? ` - ${userGroupName}` : ''}`}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Wählen Sie die Tarife aus der gleichen OU aus, die für diese Nutzergruppe verfügbar sein sollen.
            </p>
            <p className="text-xs text-amber-600 dark:text-amber-400 mt-1">
              ⚠️ Maximal ein gültiger AC-Tarif und ein gültiger DC-Tarif pro Nutzergruppe
            </p>
          </div>
          <Button
            onClick={handleSave}
            disabled={!hasChanges || saving}
            className="flex items-center"
          >
            {saving && <FiLoader className="mr-2 animate-spin" />}
            Speichern
          </Button>
        </div>

        {successMessage && (
          <div className="rounded-md bg-green-50 p-4">
            <div className="text-sm text-green-700">{successMessage}</div>
          </div>
        )}

        {error && (
          <div className="rounded-md bg-red-50 p-4">
            <div className="text-sm text-red-700">{error}</div>
          </div>
        )}

        <div className="space-y-6">
          {data.available.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400">
              Keine Tarife für diese OU verfügbar.
            </p>
          ) : (
            (() => {
              // Group tarifs by currentType
              const today = new Date();
              today.setHours(0, 0, 0, 0);

              const acTarifs = data.available.filter(tarif => tarif.currentType === 'AC');
              const dcTarifs = data.available.filter(tarif => tarif.currentType === 'DC');
              const otherTarifs = data.available.filter(tarif => !tarif.currentType || (tarif.currentType !== 'AC' && tarif.currentType !== 'DC'));

              return (
                <>
                  {/* AC Tarifs Section */}
                  {acTarifs.length > 0 && (
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                          AC-Tarife
                        </h3>
                        <span className="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                          Maximal 1 auswählbar
                        </span>
                      </div>
                      <div className="space-y-3">
                        {acTarifs.map((tarif) => (
                          <TarifCard
                            key={tarif.id}
                            tarif={tarif}
                            isSelected={selectedTarifs.includes(tarif.id)}
                            onToggle={handleTarifToggle}
                            formatPrice={formatPrice}
                            formatDate={formatDate}
                          />
                        ))}
                      </div>
                    </div>
                  )}

                  {/* DC Tarifs Section */}
                  {dcTarifs.length > 0 && (
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                          DC-Tarife
                        </h3>
                        <span className="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100">
                          Maximal 1 auswählbar
                        </span>
                      </div>
                      <div className="space-y-3">
                        {dcTarifs.map((tarif) => (
                          <TarifCard
                            key={tarif.id}
                            tarif={tarif}
                            isSelected={selectedTarifs.includes(tarif.id)}
                            onToggle={handleTarifToggle}
                            formatPrice={formatPrice}
                            formatDate={formatDate}
                          />
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Other Tarifs Section */}
                  {otherTarifs.length > 0 && (
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                          Sonstige Tarife
                        </h3>
                        <span className="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100">
                          Mehrfachauswahl möglich
                        </span>
                      </div>
                      <div className="space-y-3">
                        {otherTarifs.map((tarif) => (
                          <TarifCard
                            key={tarif.id}
                            tarif={tarif}
                            isSelected={selectedTarifs.includes(tarif.id)}
                            onToggle={handleTarifToggle}
                            formatPrice={formatPrice}
                            formatDate={formatDate}
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </>
              );
            })()
          )}
        </div>
      </div>
    </Card>
  );
};

// Separate TarifCard component for better reusability
const TarifCard = ({ tarif, isSelected, onToggle, formatPrice, formatDate }) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const isValid = new Date(tarif.validFrom) <= today && new Date(tarif.validTo) >= today;

  return (
    <div
      className={`border rounded-lg p-4 cursor-pointer transition-colors ${
        isSelected
          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
          : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
      }`}
      onClick={() => onToggle(tarif.id)}
    >
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => onToggle(tarif.id)}
            className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                {tarif.name}
              </h4>
              {tarif.currentType && (
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  tarif.currentType === 'AC'
                    ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                    : tarif.currentType === 'DC'
                    ? 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100'
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100'
                }`}>
                  {tarif.currentType}
                </span>
              )}
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                isValid
                  ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                  : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
              }`}>
                {isValid ? 'Gültig' : 'Ungültig'}
              </span>
              {tarif.optional && (
                <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                  Optional
                </span>
              )}
              {tarif.internal && (
                <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100">
                  Intern
                </span>
              )}
            </div>
            {tarif.description && (
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                {tarif.description}
              </p>
            )}
            <div className="mt-2 grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500 dark:text-gray-400">Energiepreis:</span>
                <span className="ml-1 text-gray-900 dark:text-white">
                  {formatPrice(tarif.energyPrice)}/kWh
                </span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Sitzungsgebühr:</span>
                <span className="ml-1 text-gray-900 dark:text-white">
                  {formatPrice(tarif.sessionPrice)}
                </span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Gültig von:</span>
                <span className="ml-1 text-gray-900 dark:text-white">
                  {formatDate(tarif.validFrom)}
                </span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Gültig bis:</span>
                <span className="ml-1 text-gray-900 dark:text-white">
                  {formatDate(tarif.validTo)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserGroupTarifs;
