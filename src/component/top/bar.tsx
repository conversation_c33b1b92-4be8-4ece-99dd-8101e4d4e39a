import React from "react";
import Login from "../../app/(app)/Login";
import MenuButton from "./menuButton";
import OUSelect from "../bar/OUSelect";
import NotificationWrapper from "./NotificationWrapper";

import { getServerSession } from "next-auth/next";

import { getOusBelowUserOu } from "~/server/model/ou/func";
import Breadcrumb from "~/component/top/breadcrumb";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import type { Session } from "next-auth";

import { RiUser3Fill } from "react-icons/ri";

export const exampleNotifications = [
  {
    title: "Alle Ladepunkte belegt in Franziuseck",
    description: "New message from Laur",
    time: "13 minutes ago",
    iconUrl: "", // URL for icon (if any)
    imageUrl: "", // URL for image (if any)
  },
  {
    title: "New album",
    description: "New album by <PERSON>",
    time: "1 day ago",
    iconUrl: "",
    imageUrl: "",
  },
  {
    title: "Payment completed",
    description: "Payment successfully completed",
    time: "2 days ago",
    iconUrl: "",
    imageUrl: "",
  },
];
export const revalidate = 0;

const Bar = async () => {
  const session = (await getServerSession(authOptions)) as Session;

  if (!session?.user?.email) {
    return <>No User</>;
  }

  if (!session?.user?.ou?.id) {
    return (
      <>
        User without Ou <Login />
      </>
    );
  }
  const ou = await getOusBelowUserOu(session.user.email);
  // Example data for notifications

  return (
    <>
      <nav className="relative sticky top-[1%] z-110 mx-6 mt-6 hidden flex-wrap items-center justify-between rounded-xl bg-white px-0 py-2  shadow-md backdrop-saturate-200 transition-all duration-250 ease-soft-in dark:bg-gray-950/80 dark:shadow-dark-blur sm:flex lg:flex-nowrap lg:justify-start">
        <div className="mx-auto flex w-full items-center justify-between px-4 py-1 flex-wrap-inherit">
          <nav>
            <Breadcrumb />
          </nav>
          <div
            className="mt-2 flex grow items-center sm:mr-6 sm:mt-0 md:mr-0 lg:flex lg:basis-auto"
            id="navbar"
          >
            <ul className="mb-0 flex w-full list-none flex-row justify-end pl-0 md:gap-5">
              {/* hide  if < :sm view but also when only 1 ou exist*/}
              <li className={`hidden items-center ${ou.length > 1 ? "md:flex" : ""}`}>
                <OUSelect ous={ou} defaultOuId={session.user.selectedOu.id} />
              </li>

              <li className="flex items-center">
                <i className="fa fa-user sm:mr-1" aria-hidden="true"></i>
                <span className="hidden sm:inline">
                  <Login />
                </span>
              </li>
              <li className={" flex items-center rounded-full "}>
                <a href={"/profile"}>
                  <RiUser3Fill size={20} />{" "}
                </a>
              </li>

              <li className="flex items-center pr-4">
                <NotificationWrapper />
              </li>

              <li className="flex items-center xl:hidden">
                <MenuButton />
              </li>
            </ul>
          </div>
        </div>
      </nav>
    </>
  );
};

export default Bar;
